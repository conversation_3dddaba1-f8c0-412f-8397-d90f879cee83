#!/usr/bin/env python3
"""
Download BERT base model from ModelScope (阿里云镜像)
"""
from modelscope import snapshot_download
import os
import shutil

def download_bert_model():
    """Download BERT base uncased model from ModelScope"""
    model_id = "AI-ModelScope/bert-base-uncased"
    cache_dir = "./model_cache"
    target_dir = "./bert-base-en"
    
    print(f"Downloading {model_id} from ModelScope...")
    
    try:
        # Download model to cache directory
        model_dir = snapshot_download(model_id, cache_dir=cache_dir)
        print(f"Model downloaded to: {model_dir}")
        
        # Copy files to target directory
        if os.path.exists(target_dir):
            print(f"Backing up existing {target_dir} to {target_dir}_backup")
            if os.path.exists(f"{target_dir}_backup"):
                shutil.rmtree(f"{target_dir}_backup")
            shutil.move(target_dir, f"{target_dir}_backup")
        
        shutil.copytree(model_dir, target_dir)
        print(f"Model files copied to {target_dir}")
        
        # List downloaded files
        print("\nDownloaded files:")
        for file in os.listdir(target_dir):
            file_path = os.path.join(target_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  {file}: {size:,} bytes")
                
        return True
        
    except Exception as e:
        print(f"Error downloading model: {e}")
        return False

if __name__ == "__main__":
    success = download_bert_model()
    if success:
        print("\n✅ BERT model downloaded successfully!")
    else:
        print("\n❌ Failed to download BERT model")
