#!/usr/bin/env python3
"""
Verify the environment setup for LLMeLog
"""
import torch
import transformers
import pytorch_lightning
import pandas
import numpy
import os
from transformers import AutoTokenizer, AutoModel

def check_packages():
    """Check if all required packages are installed"""
    print("📦 Checking package versions...")
    
    packages = {
        'torch': torch.__version__,
        'transformers': transformers.__version__,
        'pytorch_lightning': pytorch_lightning.__version__,
        'pandas': pandas.__version__,
        'numpy': numpy.__version__
    }
    
    for package, version in packages.items():
        print(f"  ✅ {package}: {version}")
    
    return True

def check_cuda():
    """Check CUDA availability"""
    print("\n🔥 Checking CUDA...")
    
    cuda_available = torch.cuda.is_available()
    print(f"  CUDA available: {'✅' if cuda_available else '❌'} {cuda_available}")
    
    if cuda_available:
        print(f"  CUDA version: {torch.version.cuda}")
        print(f"  GPU count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
    
    return cuda_available

def check_bert_model():
    """Check if BERT model files are complete"""
    print("\n🤖 Checking BERT model...")
    
    model_dir = "./bert-base-en"
    required_files = [
        'config.json',
        'pytorch_model.bin',
        'tokenizer.json',
        'tokenizer_config.json',
        'vocab.txt'
    ]
    
    missing_files = []
    for file in required_files:
        file_path = os.path.join(model_dir, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✅ {file}: {size:,} bytes")
        else:
            print(f"  ❌ {file}: Missing")
            missing_files.append(file)
    
    if missing_files:
        print(f"  Missing files: {missing_files}")
        return False
    
    # Try to load the model
    try:
        print("\n  Testing model loading...")
        tokenizer = AutoTokenizer.from_pretrained(model_dir)
        model = AutoModel.from_pretrained(model_dir)
        print("  ✅ Model loaded successfully")
        
        # Test tokenization
        test_text = "This is a test sentence."
        tokens = tokenizer(test_text, return_tensors="pt")
        print(f"  ✅ Tokenization test passed: {len(tokens['input_ids'][0])} tokens")
        
        return True
    except Exception as e:
        print(f"  ❌ Model loading failed: {e}")
        return False

def check_data():
    """Check if data files exist"""
    print("\n📊 Checking data files...")
    
    data_dir = "./data/hdfs"
    required_files = ['train.txt', 'test.txt', 'dev.txt']
    
    for file in required_files:
        file_path = os.path.join(data_dir, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✅ {file}: {size:,} bytes")
        else:
            print(f"  ❌ {file}: Missing")
    
    return True

def main():
    """Main verification function"""
    print("🔍 LLMeLog Environment Verification")
    print("=" * 50)
    
    checks = [
        ("Package Installation", check_packages),
        ("CUDA Support", check_cuda),
        ("BERT Model", check_bert_model),
        ("Data Files", check_data)
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"  ❌ Error in {name}: {e}")
            results.append((name, False))
    
    print("\n" + "=" * 50)
    print("📋 Verification Summary:")
    
    all_passed = True
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All checks passed! Environment is ready for LLMeLog.")
    else:
        print("\n⚠️  Some checks failed. Please fix the issues before proceeding.")
    
    return all_passed

if __name__ == "__main__":
    main()
