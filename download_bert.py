#!/usr/bin/env python3
"""
Download BERT base model from Hugging Face
"""
from transformers import BertToken<PERSON>, BertModel
import os

def download_bert_model():
    """Download BERT base uncased model and tokenizer"""
    model_name = "bert-base-uncased"
    save_directory = "./bert-base-en"
    
    print(f"Downloading {model_name} model and tokenizer...")
    
    # Download tokenizer
    tokenizer = BertTokenizer.from_pretrained(model_name)
    tokenizer.save_pretrained(save_directory)
    print(f"Tokenizer saved to {save_directory}")
    
    # Download model
    model = BertModel.from_pretrained(model_name)
    model.save_pretrained(save_directory)
    print(f"Model saved to {save_directory}")
    
    # List downloaded files
    print("\nDownloaded files:")
    for file in os.listdir(save_directory):
        file_path = os.path.join(save_directory, file)
        if os.path.isfile(file_path):
            size = os.path.getsize(file_path)
            print(f"  {file}: {size:,} bytes")

if __name__ == "__main__":
    download_bert_model()
