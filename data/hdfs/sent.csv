EventId,EventTemplate,LLM_label
bbb51b95,"block of data, Receive or transfer data block from source path to destination path, Receive or transfer data block.",0
3d91fa85,"block allocation process, Allocate a block in the NameSystem, Block allocation.",0
d38aa58d,"PacketResponder component, Handle packets for a specific block, Handle packets.",0
46003790,"received block of data, Receive a block of data from a source path, Receive a block.",0
5d5de21c,"stored block, Add a stored block to the block map, Add block.",0
44614d71,"received block of data, Receive a block of data from a source path and store it, Receive a block.",0
9c784e29,"transmitted block of data, Transmit a block of data with a specific ID, Transmit a block.",0
75627efd,"thread for transferring a block of data, Start a thread to transfer a block of data, Transfer a block.",0
54e5f6b4,"replication of a block of data, Request to replicate a block with a specific ID, Request to replicate a block.",0
4dec0816,"served block, Successfully serve or deliver a block with a specific ID, Serve or deliver a block.",0
728076ac,"replication of a block of data, Request to replicate a block with a specific ID to destination datanodes, Request to replicate a block.",0
40651754,"thread for transferring a block of data, Start a thread to transfer a block of data with a specific ID, Start a thread to transfer a block.",0
32777b38,"Verification, Successful completion of verification process, Successful verification.",0
ace40671,"writeBlock, An exception occurred while attempting to write a block of data, An exception occurred.",1
e817fa45,"PacketResponder, The PacketResponder process encountered an exception, An exception occurred.",1
bcc910df,"writeBlock, An IOException occurred while attempting to read from a stream, An IOException occurred.",1
dba996ef,"Deleting block, The event signifies the removal of a block from a specific file, Deleting block completed.",0
0567184d,"PacketReceiver, Receiving an empty packet for a specific block, Receiving an empty packet.",1
69bca6e5,"receiveBlock, An IOException occurred during the receiveBlock process due to the connection being reset by the peer, An IOException occurred.",1
d013b7a3,"writeBlock, An IOException occurred during the writeBlock process due to the connection being reset by the peer, An IOException occurred.",1
c294d20f,"block, Received redundant addStoredBlock request, It indicates a duplication or redundancy in the storage of the block.",1
a5478aff,"PacketResponder, A SocketTimeoutException occurred while waiting for a channel to be ready for reading, Timeout occurred.",1
f7c33085,"PacketResponder, An InterruptedIOException occurred while waiting for IO on a socket channel, An interrupted exception occurred.",1
f266840a,"receiveBlock, An exception occurred while receiving a block, An exception occurred.",1
15b484af,"Block file offset change, The block file offset is being changed from one value to another, along with the corresponding meta file offset, Block file offset change.",0
9621139e,"Block writing to mirror, An exception occurred while writing a block to a mirror, An exception occurred.",1
d62e5638,"receiveBlock, An exception of type InterruptedIOException occurred while waiting for IO on a socket channel during the receiveBlock operation, An exception occurred.",1
1061e5d9,"writeBlock, An exception of type InterruptedIOException occurred while waiting for IO on a socket channel during the writeBlock operation, An exception occurred.",1
2ecc047e,"PacketResponder, An IOException of type Broken pipe occurred, Broken pipe occurred.",1
4610d0f1,"writeBlock, An IOException occurred, specifying that the block is valid but cannot be written to, An IOException occurred.",1
1a718242,"System, Failed to transfer a specific file from one location to another, Failed transfer.",1
d63ef163,"NameSystemdelete function, Addition of an item to the invalidSet, invalidSet containing an additional item.",1
2e68ccc3,"Block deletion process, Error encountered while trying to delete a block, Failed block deletion.",1
8f2bc724,"NameSystemaddStoredBlock function, AddStoredBlock request received for a block not belonging to any file, Rejection or failure of the addStoredBlock request.",1
a333d363,"Item serving process, Exception encountered while serving an item, Unsuccessful serving attempt.",1
e024fa48,"PacketResponder, IOException caused by connection reset by peer, Exception thrown.",1
25382c88,"receiveBlock operation, SocketTimeoutException occurred while waiting for channel to be ready for write, Failure of the receiveBlock operation.",1
20317105,"writeBlock operation, SocketTimeoutException occurred while waiting for channel to be ready for write, Failure of the writeBlock operation.",1
06d16156,"Block being reopened, Block is being reopened, Block reopened.",0
78915d3a,"PacketResponder process, Closed By Interrupt Exception occurred, Interruption or termination of the PacketResponder process.",1
13eb7010,"neededReplications, Block removed as it does not belong to any file, Successful removal of the block from neededReplications.",0
f79898ae,"PendingReplicationMonitor, Block timed out in the PendingReplicationMonitor, Timeout.",1
124068c6,"Block being written, IOException occurred while writing the block, Error in the block writing process.",1
625a2a34,"PacketResponder process, InterruptedIOException occurred while waiting for IO on a closed SocketChannel, Error in the PacketResponder process.",1
b65fc512,"Block being written, NoRouteToHostException occurred while writing the block, Error in the block writing process.",1
fcd37a6d,"Adding an already existing block, Attempted to add a block that already exists, Block already existing.",1
559305d8,"Specific block, IOException occurred in the receiveBlock process due to a broken pipe, Error in the receiveBlock process.",1
fcf2c482,"PacketResponder process, IOException occurred indicating that the stream is closed, Error in the PacketResponder process.",1
