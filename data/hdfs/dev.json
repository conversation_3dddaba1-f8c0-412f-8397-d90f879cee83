[{"src": "Block being written, IOException occurred while writing the block, Error in the block writing process.", "tgt": 25}, {"src": "block, Received redundant addStoredBlock request, It indicates a duplication or redundancy in the storage of the block.", "tgt": 6}, {"src": "Verification, Successful completion of verification process, Successful verification.", "tgt": -1}, {"src": "Block being reopened, Block is being reopened, Block reopened.", "tgt": -1}, {"src": "PacketResponder component, Handle packets for a specific block, Handle packets.", "tgt": -1}, {"src": "Block file offset change, The block file offset is being changed from one value to another, along with the corresponding meta file offset, Block file offset change.", "tgt": -1}, {"src": "PendingReplicationMonitor, <PERSON> timed out in the PendingReplicationMonitor, Timeout.", "tgt": 24}, {"src": "stored block, Add a stored block to the block map, Add block.", "tgt": -1}, {"src": "Block deletion process, <PERSON><PERSON><PERSON> encountered while trying to delete a block, Failed block deletion.", "tgt": 17}, {"src": "writeBlock, An exception of type InterruptedIOException occurred while waiting for IO on a socket channel during the writeBlock operation, An exception occurred.", "tgt": 12}, {"src": "receiveBlock, An exception of type InterruptedIOException occurred while waiting for IO on a socket channel during the receiveBlock operation, An exception occurred.", "tgt": 11}, {"src": "PacketResponder process, InterruptedIOException occurred while waiting for IO on a closed SocketChannel, Error in the PacketResponder process.", "tgt": 26}, {"src": "PacketResponder process, Closed By Interrupt Exception occurred, Interruption or termination of the PacketResponder process.", "tgt": 23}, {"src": "PacketResponder, An IOException of type Broken pipe occurred, Broken pipe occurred.", "tgt": 13}, {"src": "receiveBlock operation, SocketTimeoutException occurred while waiting for channel to be ready for write, Failure of the receiveBlock operation.", "tgt": 21}, {"src": "NameSystemdelete function, Addition of an item to the invalidSet, invalidSet containing an additional item.", "tgt": 16}, {"src": "transmitted block of data, Transmit a block of data with a specific ID, Transmit a block.", "tgt": -1}, {"src": "thread for transferring a block of data, Start a thread to transfer a block of data with a specific ID, Start a thread to transfer a block.", "tgt": -1}, {"src": "Block being written, NoRouteToHostException occurred while writing the block, Error in the block writing process.", "tgt": 27}, {"src": "writeBlock, An IOException occurred during the writeBlock process due to the connection being reset by the peer, An IOException occurred.", "tgt": 5}, {"src": "Block writing to mirror, An exception occurred while writing a block to a mirror, An exception occurred.", "tgt": 10}, {"src": "replication of a block of data, Request to replicate a block with a specific ID to destination datanodes, Request to replicate a block.", "tgt": -1}, {"src": "Item serving process, Exception encountered while serving an item, Unsuccessful serving attempt.", "tgt": 19}, {"src": "PacketResponder, The PacketResponder process encountered an exception, An exception occurred.", "tgt": 1}, {"src": "writeBlock operation, SocketTimeoutException occurred while waiting for channel to be ready for write, Failure of the writeBlock operation.", "tgt": 22}, {"src": "NameSystemaddStoredBlock function, AddStoredBlock request received for a block not belonging to any file, Rejection or failure of the addStoredBlock request.", "tgt": 18}, {"src": "neededReplications, Block removed as it does not belong to any file, Successful removal of the block from neededReplications.", "tgt": -1}, {"src": "PacketResponder, An InterruptedIOException occurred while waiting for IO on a socket channel, An interrupted exception occurred.", "tgt": 8}, {"src": "writeBlock, An IOException occurred while attempting to read from a stream, An IOException occurred.", "tgt": 2}, {"src": "writeBlock, An IOException occurred, specifying that the block is valid but cannot be written to, An IOException occurred.", "tgt": 14}, {"src": "received block of data, Receive a block of data from a source path and store it, Receive a block.", "tgt": -1}, {"src": "block of data, Receive or transfer data block from source path to destination path, Receive or transfer data block.", "tgt": -1}, {"src": "PacketResponder, A SocketTimeoutException occurred while waiting for a channel to be ready for reading, Timeout occurred.", "tgt": 7}, {"src": "PacketResponder, IOException caused by connection reset by peer, <PERSON><PERSON> thrown.", "tgt": 20}, {"src": "writeBlock, An exception occurred while attempting to write a block of data, An exception occurred.", "tgt": 0}, {"src": "served block, Successfully serve or deliver a block with a specific ID, Serve or deliver a block.", "tgt": -1}, {"src": "PacketReceiver, Receiving an empty packet for a specific block, Receiving an empty packet.", "tgt": 3}, {"src": "block allocation process, Allocate a block in the NameSystem, Block allocation.", "tgt": -1}, {"src": "received block of data, Receive a block of data from a source path, Receive a block.", "tgt": -1}, {"src": "replication of a block of data, Request to replicate a block with a specific ID, Request to replicate a block.", "tgt": -1}, {"src": "System, Failed to transfer a specific file from one location to another, Failed transfer.", "tgt": 15}, {"src": "Deleting block, The event signifies the removal of a block from a specific file, Deleting block completed.", "tgt": -1}, {"src": "Adding an already existing block, Attempted to add a block that already exists, Block already existing.", "tgt": 28}, {"src": "thread for transferring a block of data, Start a thread to transfer a block of data, Transfer a block.", "tgt": -1}, {"src": "receiveBlock, An IOException occurred during the receiveBlock process due to the connection being reset by the peer, An IOException occurred.", "tgt": 4}, {"src": "receiveBlock, An exception occurred while receiving a block, An exception occurred.", "tgt": 9}, {"src": "PacketResponder process, IOException occurred indicating that the stream is closed, Error in the PacketResponder process.", "tgt": 30}, {"src": "Specific block, IOException occurred in the receiveBlock process due to a broken pipe, Error in the receiveBlock process.", "tgt": 29}]